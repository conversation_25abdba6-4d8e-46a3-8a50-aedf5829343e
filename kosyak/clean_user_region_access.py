#!/usr/bin/env python3
"""
Скрипт для очистки CSV файла user_region_access.csv
Удаляет строки с пустыми значениями region_id
"""

import csv
import os

def clean_csv_file(input_file, output_file):
    """
    Очищает CSV файл от строк с пустыми region_id
    
    Args:
        input_file (str): Путь к исходному CSV файлу
        output_file (str): Путь к очищенному CSV файлу
    """
    valid_rows = []
    invalid_rows = []
    
    with open(input_file, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        
        for row_num, row in enumerate(csv_reader, 1):
            # Пропускаем пустые строки
            if not row or len(row) < 4:
                continue
                
            user_id, unknown_field, region_id, has_full_access = row
            
            # Проверяем, что region_id не пустой
            if region_id and region_id.strip():
                valid_rows.append(row)
            else:
                invalid_rows.append((row_num, row))
    
    # Записываем очищенные данные
    with open(output_file, 'w', newline='', encoding='utf-8') as file:
        csv_writer = csv.writer(file)
        csv_writer.writerows(valid_rows)
    
    print(f"Обработка завершена:")
    print(f"  Валидных строк: {len(valid_rows)}")
    print(f"  Невалидных строк: {len(invalid_rows)}")
    print(f"  Очищенный файл сохранен: {output_file}")
    
    if invalid_rows:
        print(f"\nПропущенные строки (с пустым region_id):")
        for row_num, row in invalid_rows:
            print(f"  Строка {row_num}: {row}")

if __name__ == "__main__":
    input_file = "user_region_access.csv"
    output_file = "user_region_access_clean.csv"
    
    if not os.path.exists(input_file):
        print(f"Ошибка: Файл {input_file} не найден!")
        exit(1)
    
    clean_csv_file(input_file, output_file)
